// TaskResultManagement.js - 任务结果管理组件
const { useState, useEffect } = React;

function TaskResultManagement() {
    // 状态管理
    const [taskResults, setTaskResults] = useState([]);
    const [loading, setLoading] = useState(true);
    const [pagination, setPagination] = useState({
        current: 1,
        size: 10,
        total: 0,
        pages: 0
    });
    const [queryParams, setQueryParams] = useState({
        skey: '',
        cycleType: ''
    });

    // 周期类型选项
    const cycleTypeOptions = [
        { value: '', label: '全部' },
        { value: 'day', label: '日' },
        { value: 'week', label: '周' },
        { value: 'month', label: '月' }
    ];

    // 获取任务结果列表
    useEffect(() => {
        fetchTaskResults();
    }, [pagination.current, pagination.size]);

    // 从后端获取任务结果列表
    const fetchTaskResults = async () => {
        try {
            setLoading(true);
            const requestBody = {
                current: pagination.current,
                size: pagination.size,
                skey: queryParams.skey || null,
                cycleType: queryParams.cycleType || null
            };

            const response = await fetch('http://172.19.50.34:8080/ai-backend/taskResult/page', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success && result.data) {
                setTaskResults(result.data.records || []);
                setPagination({
                    current: result.data.current,
                    size: result.data.size,
                    total: result.data.total,
                    pages: result.data.pages
                });
            } else {
                console.error('Invalid response format:', result);
                setTaskResults([]);
                window.showMessage.error(result.msg || '获取任务结果列表失败');
            }
        } catch (err) {
            console.error('Error fetching task results:', err);
            setTaskResults([]);
            window.showMessage.error('获取任务结果列表失败: ' + err.message);
        } finally {
            setLoading(false);
        }
    };

    // 处理搜索
    const handleSearch = () => {
        setPagination(prev => ({ ...prev, current: 1 }));
        fetchTaskResults();
    };

    // 处理重置
    const handleReset = () => {
        setQueryParams({
            skey: '',
            cycleType: ''
        });
        setPagination(prev => ({ ...prev, current: 1 }));
        // 重置后需要重新查询
        setTimeout(() => {
            fetchTaskResults();
        }, 0);
    };

    // 处理分页变化
    const handlePageChange = (newPage) => {
        setPagination(prev => ({ ...prev, current: newPage }));
    };

    // 处理每页大小变化
    const handlePageSizeChange = (newSize) => {
        setPagination(prev => ({ ...prev, size: newSize, current: 1 }));
    };

    // 格式化日期
    const formatDate = (dateString) => {
        if (!dateString) return '-';
        return new Date(dateString).toLocaleString('zh-CN');
    };

    // 格式化周期类型
    const formatCycleType = (cycleType) => {
        const option = cycleTypeOptions.find(opt => opt.value === cycleType);
        return option ? option.label : cycleType || '-';
    };

    // 渲染分页组件
    const renderPagination = () => {
        if (pagination.total === 0) return null;

        const startPage = Math.max(1, pagination.current - 2);
        const endPage = Math.min(pagination.pages, pagination.current + 2);
        const pages = [];

        for (let i = startPage; i <= endPage; i++) {
            pages.push(i);
        }

        return (
            <div className="flex items-center justify-between mt-6">
                <div className="text-sm text-gray-700">
                    共 {pagination.total} 条记录，第 {pagination.current} / {pagination.pages} 页
                </div>
                <div className="flex items-center space-x-2">
                    <button
                        onClick={() => handlePageChange(1)}
                        disabled={pagination.current === 1}
                        className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:bg-gray-100 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                        首页
                    </button>
                    <button
                        onClick={() => handlePageChange(pagination.current - 1)}
                        disabled={pagination.current === 1}
                        className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:bg-gray-100 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                        上一页
                    </button>
                    
                    {pages.map(page => (
                        <button
                            key={page}
                            onClick={() => handlePageChange(page)}
                            className={`px-3 py-1 border rounded-md text-sm ${
                                page === pagination.current
                                    ? 'bg-blue-500 text-white border-blue-500'
                                    : 'border-gray-300 hover:bg-gray-50'
                            }`}
                        >
                            {page}
                        </button>
                    ))}
                    
                    <button
                        onClick={() => handlePageChange(pagination.current + 1)}
                        disabled={pagination.current === pagination.pages}
                        className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:bg-gray-100 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                        下一页
                    </button>
                    <button
                        onClick={() => handlePageChange(pagination.pages)}
                        disabled={pagination.current === pagination.pages}
                        className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:bg-gray-100 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                        末页
                    </button>
                    
                    <select
                        value={pagination.size}
                        onChange={(e) => handlePageSizeChange(parseInt(e.target.value))}
                        className="px-2 py-1 border border-gray-300 rounded-md text-sm"
                    >
                        <option value={10}>10条/页</option>
                        <option value={20}>20条/页</option>
                        <option value={50}>50条/页</option>
                        <option value={100}>100条/页</option>
                    </select>
                </div>
            </div>
        );
    };

    // 主渲染函数
    return (
        <div className="h-full flex flex-col text-gray-800">
            <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold">任务结果管理</h2>
            </div>

            {/* 搜索条件 */}
            <div className="bg-white bg-opacity-80 rounded-xl shadow-sm p-4 mb-6">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">搜索关键字</label>
                        <input
                            type="text"
                            value={queryParams.skey}
                            onChange={(e) => setQueryParams(prev => ({ ...prev, skey: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="输入skey搜索"
                        />
                    </div>
                    
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">周期类型</label>
                        <select
                            value={queryParams.cycleType}
                            onChange={(e) => setQueryParams(prev => ({ ...prev, cycleType: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            {cycleTypeOptions.map(option => (
                                <option key={option.value} value={option.value}>
                                    {option.label}
                                </option>
                            ))}
                        </select>
                    </div>
                    
                    <div className="flex items-end space-x-2">
                        <button
                            onClick={handleSearch}
                            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                        >
                            搜索
                        </button>
                        <button
                            onClick={handleReset}
                            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                        >
                            重置
                        </button>
                    </div>
                </div>
            </div>

            {/* 数据表格 */}
            <div className="bg-white bg-opacity-80 rounded-xl shadow-sm flex-1 overflow-hidden">
                {loading ? (
                    <div className="flex justify-center items-center h-64">
                        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                    </div>
                ) : (
                    <div className="h-full flex flex-col">
                        <div className="overflow-x-auto flex-1">
                            <table className="w-full">
                                <thead className="bg-gray-50 border-b border-gray-200">
                                    <tr>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Skey</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">任务ID</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">周期类型</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">提示词</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">执行开始时间</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">执行结束时间</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {taskResults.length === 0 ? (
                                        <tr>
                                            <td colSpan="8" className="px-4 py-8 text-center text-gray-500">
                                                暂无数据
                                            </td>
                                        </tr>
                                    ) : (
                                        taskResults.map(result => (
                                            <tr key={result.id} className="hover:bg-gray-50">
                                                <td className="px-4 py-3 text-sm text-gray-900">{result.id}</td>
                                                <td className="px-4 py-3 text-sm text-gray-900">{result.skey || '-'}</td>
                                                <td className="px-4 py-3 text-sm text-gray-900">{result.taskId || '-'}</td>
                                                <td className="px-4 py-3 text-sm text-gray-900">
                                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                                        result.cycleType === 'day' ? 'bg-green-100 text-green-800' :
                                                        result.cycleType === 'week' ? 'bg-blue-100 text-blue-800' :
                                                        result.cycleType === 'month' ? 'bg-purple-100 text-purple-800' :
                                                        'bg-gray-100 text-gray-800'
                                                    }`}>
                                                        {formatCycleType(result.cycleType)}
                                                    </span>
                                                </td>
                                                <td className="px-4 py-3 text-sm text-gray-900 max-w-xs truncate" title={result.prompt}>
                                                    {result.prompt || '-'}
                                                </td>
                                                <td className="px-4 py-3 text-sm text-gray-900">{formatDate(result.execStartTime)}</td>
                                                <td className="px-4 py-3 text-sm text-gray-900">{formatDate(result.execEndTime)}</td>
                                                <td className="px-4 py-3 text-sm text-gray-900">{formatDate(result.createTime)}</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </table>
                        </div>
                        
                        {/* 分页组件 */}
                        <div className="px-4 py-3 border-t border-gray-200">
                            {renderPagination()}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}
