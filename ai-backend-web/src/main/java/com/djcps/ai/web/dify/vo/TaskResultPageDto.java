package com.djcps.ai.web.dify.vo;

import com.djcps.ai.dao.entity.TaskResult;
import lombok.Data;

import java.util.List;

/**
 * TaskResult分页响应DTO
 */
@Data
public class TaskResultPageDto {
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 当前页
     */
    private Long current;
    
    /**
     * 每页大小
     */
    private Long size;
    
    /**
     * 总页数
     */
    private Long pages;
    
    /**
     * 数据列表
     */
    private List<TaskResult> records;
}
